# 🏗️ Restoran POS - Geliştirm<PERSON> Kuralları

> **Bu dosya projenin tüm geliştirme sürecinde uyulması gereken altın kuralları içerir.**

## 📁 **1. KLASÖR YAPISI KURALLARI**

### ✅ **Monorepo Yapısı:**
```
atropos/
├── desktop/     # Electron + React (Frontend)
├── server/      # Node.js + Express (Backend)
├── shared/      # Ortak tipler ve sabitler
├── prisma/      # Database schema ve migrations
├── docs/        # Dokümantasyon
└── .vscode/     # VS Code ayarları
```

### ✅ **Desktop Klasör Yapısı:**
```
desktop/src/
├── components/  # Yeniden kullanılabilir UI bileşenleri
├── pages/       # Sayfa bileşenleri
├── layouts/     # Layout bileşenleri
├── hooks/       # Custom React hooks
├── services/    # API çağrıları
├── store/       # State management (Zustand)
├── utils/       # Yardımcı fonksiyonlar
├── constants/   # Sabitler (re<PERSON><PERSON>, boy<PERSON><PERSON>)
├── contexts/    # React Context'ler
├── i18n/        # Dil dosyaları
└── theme/       # Tema yapılandırması
```

### ✅ **Server Klasör Yapısı:**
```
server/src/
├── controllers/ # Route handler'ları
├── routes/      # API route tanımları
├── middlewares/ # Express middleware'ler
├── services/    # İş mantığı
├── utils/       # Yardımcı fonksiyonlar
├── validators/  # Input validation
└── types/       # TypeScript tipleri
```

## 🎨 **2. İSİMLENDİRME KURALLARI**

### ✅ **Dosya İsimlendirme:**
- **React Bileşenleri**: `PascalCase.tsx` → `OrderCard.tsx`
- **Hooks**: `camelCase.ts` → `useOrderData.ts`
- **Utils**: `camelCase.ts` → `formatCurrency.ts`
- **Constants**: `camelCase.ts` → `apiEndpoints.ts`
- **Types**: `PascalCase.ts` → `OrderTypes.ts`

### ✅ **Değişken İsimlendirme:**
- **Değişkenler**: `camelCase` → `orderTotal`
- **Sabitler**: `UPPER_SNAKE_CASE` → `API_BASE_URL`
- **Bileşenler**: `PascalCase` → `OrderCard`
- **Fonksiyonlar**: `camelCase` → `calculateTotal`

### ✅ **Database İsimlendirme:**
- **Tablolar**: `PascalCase` → `Order`, `OrderItem`
- **Alanlar**: `camelCase` → `orderNumber`, `totalAmount`
- **Enum'lar**: `UPPER_CASE` → `PENDING`, `COMPLETED`

## 🌍 **3. DİL (i18n) KURALLARI**

### ✅ **Dil Dosyası Yapısı:**
```json
{
  "menu": { "orders": "Siparişler" },
  "common": { "save": "Kaydet" },
  "orders": { "newOrder": "Yeni Sipariş" }
}
```

### ✅ **Çeviri Anahtarları:**
- **Hiyerarşik yapı**: `menu.orders`, `common.save`
- **Açıklayıcı isimler**: `orders.newOrder` ✅, `btn1` ❌
- **Tutarlı gruplandırma**: `common.*`, `orders.*`, `products.*`

### ✅ **Kullanım:**
```typescript
const { t } = useTranslation()
return <Button>{t('common.save')}</Button>
```

## 🎨 **4. TEMA VE RENK KURALLARI**

### ✅ **Renk Kullanımı:**
```typescript
// ✅ Doğru - Semantic renkler
<Button color="primary">
<Chip color="success">
<Alert severity="error">

// ❌ Yanlış - Hard-coded renkler
<Button sx={{ backgroundColor: '#1976d2' }}>
```

### ✅ **Tema Sabitleri:**
```typescript
// colors.primary.main ✅
// semanticColors.order.pending ✅
// spacing.md ✅
// borderRadius.lg ✅
```

## 📦 **5. COMPONENT KURALLARI**

### ✅ **Bileşen Yapısı:**
```typescript
// 1. Imports
import React from 'react'
import { useTranslation } from 'react-i18next'

// 2. Types
interface OrderCardProps {
  order: Order
  onEdit: (id: string) => void
}

// 3. Component
export const OrderCard: React.FC<OrderCardProps> = ({ order, onEdit }) => {
  const { t } = useTranslation()
  
  return (
    <Card>
      <Typography>{t('orders.orderNumber')}: {order.orderNumber}</Typography>
    </Card>
  )
}
```

### ✅ **Props Kuralları:**
- **Interface tanımla**: `ComponentNameProps`
- **Optional props**: `prop?:` kullan
- **Event handlers**: `onAction` formatında

## 🔄 **6. STATE MANAGEMENT KURALLARI**

### ✅ **Zustand Store Yapısı:**
```typescript
interface OrderStore {
  // State
  orders: Order[]
  loading: boolean
  
  // Actions
  fetchOrders: () => Promise<void>
  addOrder: (order: Order) => void
  updateOrder: (id: string, data: Partial<Order>) => void
}
```

### ✅ **Store İsimlendirme:**
- **Store dosyası**: `useOrderStore.ts`
- **Hook kullanımı**: `const { orders, fetchOrders } = useOrderStore()`

## 🌐 **7. API KURALLARI**

### ✅ **Endpoint Yapısı:**
```typescript
// RESTful API
GET    /api/orders          # Liste
POST   /api/orders          # Oluştur
GET    /api/orders/:id      # Detay
PUT    /api/orders/:id      # Güncelle
DELETE /api/orders/:id      # Sil
```

### ✅ **Response Formatı:**
```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}
```

## 🎯 **8. TYPESCRIPT KURALLARI**

### ✅ **Type Tanımlama:**
```typescript
// ✅ Interface kullan (genişletilebilir)
interface Order {
  id: string
  orderNumber: string
}

// ✅ Union types
type OrderStatus = 'PENDING' | 'COMPLETED' | 'CANCELLED'

// ✅ Generic types
interface ApiResponse<T> {
  data: T
}
```

### ✅ **Import/Export:**
```typescript
// ✅ Named exports tercih et
export const OrderCard = () => {}
export { OrderCard }

// ✅ Barrel exports
export * from './OrderCard'
export * from './OrderList'
```

## 🚀 **HIZLI REFERANS**

### ✅ **Yeni Bileşen Oluştururken:**
1. `components/` klasöründe `PascalCase.tsx` oluştur
2. Props interface tanımla
3. `useTranslation` hook'u ekle
4. Semantic renkler kullan
5. Test dosyası oluştur

### ✅ **Yeni API Endpoint Oluştururken:**
1. `routes/` klasöründe route tanımla
2. `controllers/` klasöründe handler yaz
3. `validators/` klasöründe validation ekle
4. `ApiResponse<T>` formatını kullan
5. Error handling ekle

### ✅ **Yeni Çeviri Eklerken:**
1. `i18n/locales/tr.json` ve `en.json` güncelle
2. Hiyerarşik yapı kullan
3. Açıklayıcı anahtar isimleri
4. `t('key')` ile kullan

---

## 🚨 **11. ERROR HANDLING KURALLARI**

### ✅ **Standart Error Pattern:**
```typescript
// ✅ Error handling pattern
try {
  const result = await someOperation()
  return { success: true, data: result }
} catch (error) {
  logger.error('Operation failed:', error)
  throw new AppError('Operation failed', 500, 'OPERATION_FAILED')
}

// Custom Error Class
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}
```

### ✅ **Error Response Format:**
```typescript
interface ErrorResponse {
  success: false
  error: string
  code?: string
  details?: any
}
```

## 🔐 **12. AUTHENTICATION/AUTHORIZATION PATTERN**

### ✅ **Auth Middleware Pattern:**
```typescript
// ✅ Auth Middleware Pattern
export const requireAuth = (roles?: UserRole[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = req.headers.authorization?.split(' ')[1]
      const user = await verifyToken(token)

      if (roles && !roles.includes(user.role)) {
        throw new AppError('Insufficient permissions', 403, 'FORBIDDEN')
      }

      req.user = user
      next()
    } catch (error) {
      next(error)
    }
  }
}

// Kullanım
router.get('/admin', requireAuth([UserRole.ADMIN]), getAdminData)
```

## ✅ **13. VALIDATION KURALLARI**

### ✅ **Zod Schema Pattern:**
```typescript
// ✅ Validation Schema Pattern
import { z } from 'zod'

export const loginSchema = z.object({
  username: z.string().min(3).max(50),
  password: z.string().min(6),
  pin: z.string().length(4).optional()
})

export type LoginInput = z.infer<typeof loginSchema>

// Middleware
export const validate = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          errors: error.errors
        })
      }
    }
  }
}
```

## 🗄️ **14. DATABASE TRANSACTION PATTERN**

### ✅ **Prisma Transaction Pattern:**
```typescript
// ✅ Transaction Pattern
const createOrderWithItems = async (orderData: CreateOrderInput) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Create order
    const order = await tx.order.create({
      data: orderData
    })

    // 2. Create order items
    const items = await tx.orderItem.createMany({
      data: orderData.items.map(item => ({
        ...item,
        orderId: order.id
      }))
    })

    // 3. Update stock
    for (const item of orderData.items) {
      await tx.inventoryItem.update({
        where: { productId: item.productId },
        data: {
          currentStock: { decrement: item.quantity }
        }
      })
    }

    return order
  })
}
```

## 📝 **15. LOGGER KURALLARI**

### ✅ **Winston Logger Pattern:**
```typescript
// ✅ Logger Pattern
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
})

// Kullanım
logger.info('User logged in', { userId: user.id })
logger.error('Order creation failed', { error, orderData })
```

## 🔌 **16. SOCKET.IO EVENT NAMING**

### ✅ **Event Naming Convention:**
```typescript
// ✅ Socket Event Naming
export const SocketEvents = {
  // Client -> Server
  JOIN_BRANCH: 'join:branch',
  LEAVE_BRANCH: 'leave:branch',
  ORDER_CREATE: 'order:create',
  ORDER_UPDATE: 'order:update',

  // Server -> Client
  ORDER_CREATED: 'order:created',
  ORDER_UPDATED: 'order:updated',
  ORDER_STATUS_CHANGED: 'order:statusChanged',
  TABLE_STATUS_CHANGED: 'table:statusChanged'
} as const
```

## 🌍 **17. ENVIRONMENT VARIABLES TYPING**

### ✅ **Environment Types:**
```typescript
// shared/src/types/env.d.ts
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test'
      DATABASE_URL: string
      JWT_SECRET: string
      JWT_EXPIRE: string
      PORT: string
      CLIENT_URL: string
    }
  }
}
```

## 🖥️ **18. ELECTRON OPTIMIZASYON KURALLARI**

### ✅ **BrowserWindow Ayarları:**
```javascript
// ✅ Optimal Electron pencere ayarları
mainWindow = new BrowserWindow({
  width: 1400,
  height: 900,
  minWidth: 1200,
  minHeight: 800,
  show: false, // Pencere hazır olana kadar gizle
  center: true,
  autoHideMenuBar: true,
  webPreferences: {
    zoomFactor: 1.0, // Zoom seviyesini sabitle
    nodeIntegration: false,
    contextIsolation: true
  }
})

// Pencere hazır olduğunda göster
mainWindow.once('ready-to-show', () => {
  mainWindow.show()
})
```

### ✅ **CSS Global Reset (Electron için):**
```css
/* ✅ Scrollbar'a izin ver ama kontrollü */
html, body, #root {
  height: 100%;
  width: 100%;
  overflow: auto; /* Gerektiğinde scroll göster */
}

/* ✅ Custom scrollbar - sadece gerekli yerlerde */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
```

### ✅ **Layout Kuralları:**
```typescript
// ✅ Full viewport layout - Scrollbar'a izin ver
<Box sx={{
  height: '100vh',
  width: '100vw',
  overflow: 'auto', // Gerektiğinde scroll
  display: 'flex',
  flexDirection: 'column'
}}>

// ✅ Scrollable content - Custom scrollbar ile
<Box sx={{
  flex: 1,
  overflow: 'auto',
  '&::-webkit-scrollbar': { width: '6px' },
  '&::-webkit-scrollbar-track': { background: 'transparent' },
  '&::-webkit-scrollbar-thumb': {
    background: 'rgba(0, 0, 0, 0.2)',
    borderRadius: '3px',
    '&:hover': { background: 'rgba(0, 0, 0, 0.3)' }
  }
}}>
```

## 🎨 **19. UI/UX COMPONENT KURALLARI**

### ✅ **Header Component Pattern:**
```typescript
// ✅ Responsive header yapısı
<Box sx={{
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  px: { xs: 2, sm: 3 },
  py: { xs: 1.5, sm: 2 },
  flexWrap: { xs: 'wrap', md: 'nowrap' },
  gap: { xs: 1, sm: 2 }
}}>
```

### ✅ **Connection Status Pattern:**
```typescript
// ✅ Bağlantı durumu gösterimi
const useConnectionStatus = () => {
  const [status, setStatus] = useState({
    internet: navigator.onLine,
    backend: false
  })

  // Internet monitoring
  useEffect(() => {
    const handleOnline = () => setStatus(prev => ({ ...prev, internet: true }))
    const handleOffline = () => setStatus(prev => ({ ...prev, internet: false }))

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
}
```

### ✅ **Theme & Language Toggle Pattern:**
```typescript
// ✅ Minimal tema/dil değiştirme butonları
<Tooltip title={t('header.theme.tooltip')}>
  <IconButton onClick={toggleTheme} size="small">
    {themeMode === 'light' ? <DarkMode /> : <LightMode />}
  </IconButton>
</Tooltip>

<Tooltip title={t('header.language.tooltip')}>
  <IconButton onClick={toggleLanguage} size="small">
    <Typography variant="caption" fontWeight="bold">
      {getCurrentLanguage()}
    </Typography>
  </IconButton>
</Tooltip>
```

## 🔧 **20. PERFORMANCE KURALLARI**

### ✅ **Viewport Optimizasyonu:**
```html
<!-- ✅ Electron için optimize viewport -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
```

### ✅ **Memory Management:**
```typescript
// ✅ useEffect cleanup
useEffect(() => {
  const interval = setInterval(checkConnection, 30000)
  return () => clearInterval(interval) // Cleanup
}, [])

// ✅ Event listener cleanup
useEffect(() => {
  const handleResize = () => { /* ... */ }
  window.addEventListener('resize', handleResize)
  return () => window.removeEventListener('resize', handleResize)
}, [])
```

### ✅ **Bundle Optimization:**
```typescript
// ✅ Lazy loading
const DashboardPage = lazy(() => import('./pages/dashboard/DashboardPage'))

// ✅ Code splitting
const routes = [
  {
    path: '/dashboard',
    component: lazy(() => import('./pages/dashboard/DashboardPage'))
  }
]
```

**Bu kurallar projenin tutarlılığını ve kalitesini garanti eder! 🎯**


# ⚡ Hızlı Referans Kartı

## 📁 **Dosya Oluşturma Şablonları**

### 🔸 **React Component:**
```typescript
// components/OrderCard.tsx
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Card, Typography } from '@mui/material'

interface OrderCardProps {
  order: Order
  onEdit?: (id: string) => void
}

export const OrderCard: React.FC<OrderCardProps> = ({ order, onEdit }) => {
  const { t } = useTranslation()
  
  return (
    <Card>
      <Typography>{t('orders.orderNumber')}: {order.orderNumber}</Typography>
    </Card>
  )
}
```

### 🔸 **Custom Hook:**
```typescript
// hooks/useOrderData.ts
import { useState, useEffect } from 'react'
import { Order } from '@shared/types'

export const useOrderData = (orderId: string) => {
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    // API call logic
  }, [orderId])
  
  return { order, loading }
}
```

### 🔸 **Zustand Store:**
```typescript
// store/useOrderStore.ts
import { create } from 'zustand'
import { Order } from '@shared/types'

interface OrderStore {
  orders: Order[]
  loading: boolean
  fetchOrders: () => Promise<void>
  addOrder: (order: Order) => void
}

export const useOrderStore = create<OrderStore>((set, get) => ({
  orders: [],
  loading: false,
  
  fetchOrders: async () => {
    set({ loading: true })
    // API call
    set({ loading: false })
  },
  
  addOrder: (order) => {
    set(state => ({ orders: [...state.orders, order] }))
  }
}))
```

### 🔸 **API Controller:**
```typescript
// server/src/controllers/orderController.ts
import { Request, Response } from 'express'
import { ApiResponse } from '@shared/types'

export const getOrders = async (req: Request, res: Response) => {
  try {
    const orders = await orderService.getAll()
    
    const response: ApiResponse<Order[]> = {
      success: true,
      data: orders
    }
    
    res.json(response)
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch orders'
    })
  }
}
```

## 🎨 **Renk Kullanım Örnekleri**

```typescript
// ✅ Doğru kullanım
<Button color="primary">Kaydet</Button>
<Chip color="success">Tamamlandı</Chip>
<Alert severity="error">Hata mesajı</Alert>

// ✅ Custom renkler
<Box sx={{ backgroundColor: semanticColors.order.pending }}>
<Typography color={colors.primary.main}>
```

## 🌍 **i18n Kullanım Örnekleri**

```typescript
// ✅ Basit çeviri
const { t } = useTranslation()
<Typography>{t('common.save')}</Typography>

// ✅ Parametreli çeviri
<Typography>{t('orders.itemCount', { count: 5 })}</Typography>

// ✅ Çoğul çeviri
<Typography>{t('orders.items', { count })}</Typography>
```

## 📦 **Import/Export Örnekleri**

```typescript
// ✅ Named imports
import { OrderCard, OrderList } from '@/components'
import { useOrderStore } from '@/store'
import { colors, spacing } from '@/constants'

// ✅ Barrel exports (index.ts)
export { OrderCard } from './OrderCard'
export { OrderList } from './OrderList'
export * from './OrderTypes'
```

## 🔄 **State Management Örnekleri**

```typescript
// ✅ Zustand kullanımı
const { orders, loading, fetchOrders } = useOrderStore()

// ✅ Local state
const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)

// ✅ Form state
const { register, handleSubmit, formState: { errors } } = useForm<OrderForm>()
```

## 🌐 **API Endpoint Örnekleri**

```typescript
// ✅ RESTful endpoints
GET    /api/orders              # Tüm siparişler
POST   /api/orders              # Yeni sipariş
GET    /api/orders/:id          # Sipariş detayı
PUT    /api/orders/:id          # Sipariş güncelle
DELETE /api/orders/:id          # Sipariş sil
GET    /api/orders/:id/items    # Sipariş kalemleri
```


**Bu referans kartını her zaman yanında bulundur! 🎯**
