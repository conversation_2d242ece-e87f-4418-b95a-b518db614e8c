// Common API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// User Types
export interface User {
  id: string
  username: string
  firstName: string
  lastName: string
  email?: string
  role: UserRole
  active: boolean
  companyId: string
  branchId: string
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

// Auth Types
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  user: {
    id: string
    username: string
    firstName: string
    lastName: string
    email?: string
    role: string
    companyId: string
    branchId: string
  }
  token: string
}

export interface AuthUser {
  userId: string
  username: string
  role: string
  companyId: string
  branchId: string
}

export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  BRANCH_MANAGER = '<PERSON><PERSON>CH_MANAGER',
  CASHIER = 'CASHIER',
  WAITER = 'WAITER',
  KITCHEN = 'KITCHEN',
  REPORTER = 'REPORTER',
  COURIER = 'COURIER',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE'
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  orderType: OrderType
  status: OrderStatus
  totalAmount: number
  createdAt: string
  updatedAt: string
}

export enum OrderType {
  DINE_IN = 'DINE_IN',
  TAKEAWAY = 'TAKEAWAY',
  DELIVERY = 'DELIVERY',
  ONLINE = 'ONLINE'
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PREPARING = 'PREPARING',
  READY = 'READY',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}
