// Product Routes - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 7: RESTful API Endpoint yapısı
// ✅ Kural 12: Authentication/Authorization Pattern
// ✅ Kural 13: Validation Middleware kullanımı

import { Router } from 'express'
import {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getCategories,
  getTaxes
} from '../controllers/productController'
import { 
  authenticateToken, 
  requireManager, 
  requireAdmin 
} from '../middlewares/authMiddleware'
import {
  validate,
  validateQuery,
  validateParams,
  createProductSchema,
  updateProductSchema,
  productQuerySchema,
  productIdSchema
} from '../validators/productValidators'
import rateLimit from 'express-rate-limit'

const router = Router()

// ✅ Rate limiting for product operations
const productLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 100, // 15 dakikada maksimum 100 istek
  message: {
    success: false,
    error: 'Çok fazla istek. 15 dakika sonra tekrar deneyin.'
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// ✅ Create/Update operations için daha sıkı limit
const productModifyLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 dakika
  max: 20, // 5 dakikada maksimum 20 oluşturma/güncelleme
  message: {
    success: false,
    error: 'Çok fazla ürün işlemi. 5 dakika sonra tekrar deneyin.'
  }
})

/**
 * ✅ Kural 7: GET /api/products - Ürün listesi (paginated)
 * @desc    Şirkete ait ürünleri listele
 * @access  Private (Manager+)
 */
router.get(
  '/',
  productLimiter,
  authenticateToken,
  requireManager,
  // validateQuery(productQuerySchema), // Geçici olarak kapatıldı
  getProducts
)

/**
 * ✅ Kural 7: GET /api/products/:id - Ürün detayı
 * @desc    Belirli bir ürünün detaylarını getir
 * @access  Private (Manager+)
 */
router.get(
  '/:id',
  productLimiter,
  authenticateToken,
  requireManager,
  validateParams(productIdSchema),
  getProductById
)

/**
 * ✅ Kural 7: POST /api/products - Yeni ürün oluştur
 * @desc    Yeni ürün oluştur
 * @access  Private (Manager+)
 */
router.post(
  '/',
  // productModifyLimiter, // Geçici olarak kapatıldı
  authenticateToken,
  requireManager,
  // validate(createProductSchema), // Geçici olarak kapatıldı
  createProduct
)

/**
 * ✅ Kural 7: PUT /api/products/:id - Ürün güncelle
 * @desc    Mevcut ürünü güncelle
 * @access  Private (Manager+)
 */
router.put(
  '/:id',
  productModifyLimiter,
  authenticateToken,
  requireManager,
  validateParams(productIdSchema),
  validate(updateProductSchema),
  updateProduct
)

/**
 * ✅ Kural 7: DELETE /api/products/:id - Ürün sil (soft delete)
 * @desc    Ürünü sil (soft delete)
 * @access  Private (Admin+)
 */
router.delete(
  '/:id',
  productModifyLimiter,
  authenticateToken,
  requireAdmin, // Sadece admin silebilir
  validateParams(productIdSchema),
  deleteProduct
)

// ✅ Helper endpoints for dropdowns
/**
 * @route   GET /api/products/helpers/categories
 * @desc    Dropdown için kategori listesi
 * @access  Private (Manager+)
 */
router.get(
  '/helpers/categories',
  productLimiter,
  authenticateToken,
  requireManager,
  getCategories
)

/**
 * @route   GET /api/products/helpers/taxes
 * @desc    Dropdown için vergi oranları listesi
 * @access  Private (Manager+)
 */
router.get(
  '/helpers/taxes',
  productLimiter,
  authenticateToken,
  requireManager,
  getTaxes
)

export default router
