// Product Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 6: Service Layer Pattern
// ✅ Kural 11: Error Handling Pattern
// ✅ Kural 14: Prisma Transaction Pattern

import { PrismaClient, Prisma } from '@prisma/client'
import {
  ApiResponse,
  PaginatedResponse,
  Product,
  Category
} from '@shared/types'
import { 
  CreateProductInput, 
  UpdateProductInput, 
  ProductQueryInput 
} from '../validators/productValidators'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'

const prisma = new PrismaClient()

// ✅ Utility function to convert Prisma Decimal to number
const transformProduct = (product: Record<string, unknown>): Product => {
  return {
    ...product,
    basePrice: Number(product.basePrice),
    costPrice: product.costPrice ? Number(product.costPrice) : undefined,
    profitMargin: product.profitMargin ? Number(product.profitMargin) : undefined,
    criticalStock: product.criticalStock ? Number(product.criticalStock) : undefined,
    category: product.category ? {
      ...(product.category as Record<string, unknown>)
    } : undefined,
    tax: product.tax ? {
      ...(product.tax as Record<string, unknown>),
      rate: Number((product.tax as Record<string, unknown>).rate)
    } : undefined,
    variants: (product.variants as Record<string, unknown>[])?.map((variant: Record<string, unknown>) => ({
      ...variant,
      price: Number(variant.price),
      costPrice: variant.costPrice ? Number(variant.costPrice) : undefined
    }))
  } as Product
}

export class ProductService {
  
  /**
   * ✅ Kural 7: Paginated list endpoint
   */
  async getProducts(
    companyId: string, 
    query: ProductQueryInput
  ): Promise<PaginatedResponse<Product>> {
    try {
      logger.info('Fetching products', { companyId, query })

      const {
        page,
        limit,
        search,
        categoryId,
        available,
        sellable,
        featured,
        hasVariants,
        hasModifiers,
        sortBy,
        sortOrder
      } = query

      // ✅ Where conditions
      const where: Record<string, unknown> = {
        companyId,
        deletedAt: null
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { barcode: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (categoryId) where.categoryId = categoryId
      if (available !== undefined) where.available = available
      if (sellable !== undefined) where.sellable = sellable
      if (featured !== undefined) where.featured = featured
      if (hasVariants !== undefined) where.hasVariants = hasVariants
      if (hasModifiers !== undefined) where.hasModifiers = hasModifiers

      // ✅ Sorting
      const orderBy: Record<string, string> = {}
      orderBy[sortBy] = sortOrder

      // ✅ Pagination
      const skip = (page - 1) * limit
      const take = limit

      // ✅ En basit test - sadece findMany
      const products = await prisma.product.findMany({
        where: {
          companyId,
          deletedAt: null
        },
        take: 10
      })

      const total = products.length

      const totalPages = Math.ceil(total / limit)

      logger.info('Products fetched successfully', { 
        companyId, 
        count: products.length, 
        total 
      })

      return {
        success: true,
        data: products.map(transformProduct),
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('Failed to fetch products', { error, companyId, query })
      throw new AppError(
        'Ürünler getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }

  /**
   * ✅ Kural 7: Get single product
   */
  async getProductById(companyId: string, productId: string): Promise<ApiResponse<Product>> {
    try {
      logger.info('Fetching product by ID', { companyId, productId })

      const product = await prisma.product.findFirst({
        where: {
          id: productId,
          companyId,
          deletedAt: null
        },
        include: {
          category: true,
          tax: true,
          variants: {
            where: { deletedAt: null },
            orderBy: { displayOrder: 'asc' }
          },
          modifierGroups: {
            include: {
              modifierGroup: {
                include: {
                  modifiers: {
                    where: { deletedAt: null },
                    orderBy: { displayOrder: 'asc' }
                  }
                }
              }
            }
          }
        }
      })

      if (!product) {
        throw new AppError(
          'Ürün bulunamadı',
          404,
          ErrorCodes.PRODUCT_NOT_FOUND
        )
      }

      logger.info('Product fetched successfully', { productId })

      return {
        success: true,
        data: transformProduct(product),
        message: 'Ürün başarıyla getirildi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error
      
      logger.error('Failed to fetch product', { error, companyId, productId })
      throw new AppError(
        'Ürün getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }

  /**
   * ✅ Kural 14: Transaction pattern ile create
   */
  async createProduct(
    companyId: string, 
    data: CreateProductInput
  ): Promise<ApiResponse<Product>> {
    try {
      logger.info('Creating product', { companyId, data: { ...data, images: data.images?.length } })

      // ✅ Kural 14: Prisma Transaction Pattern
      const product = await prisma.$transaction(async (tx) => {
        // 1. Validations
        await this.validateProductData(tx, companyId, data)

        // 2. Check unique constraints
        await this.checkUniqueConstraints(tx, companyId, data.code, data.barcode)

        // 3. Create product
        const newProduct = await tx.product.create({
          data: {
            ...data,
            companyId,
            images: data.images || []
          },
          include: {
            category: true,
            tax: true
          }
        })

        logger.info('Product created successfully', { 
          productId: newProduct.id, 
          code: newProduct.code 
        })

        return newProduct
      })

      return {
        success: true,
        data: transformProduct(product),
        message: 'Ürün başarıyla oluşturuldu'
      }
    } catch (error) {
      if (error instanceof AppError) throw error
      
      logger.error('Failed to create product', { error, companyId, data })
      throw new AppError(
        'Ürün oluşturulamadı',
        500,
        ErrorCodes.PRODUCT_CREATION_FAILED,
        error
      )
    }
  }

  /**
   * ✅ Private validation methods
   */
  private async validateProductData(
    tx: Prisma.TransactionClient,
    companyId: string,
    data: CreateProductInput | UpdateProductInput
  ): Promise<void> {
    // Category validation
    if (data.categoryId) {
      const category = await tx.category.findFirst({
        where: {
          id: data.categoryId,
          companyId,
          deletedAt: null
        }
      })

      if (!category) {
        throw new AppError(
          'Kategori bulunamadı',
          400,
          ErrorCodes.PRODUCT_CATEGORY_NOT_FOUND
        )
      }
    }

    // Tax validation
    if (data.taxId) {
      const tax = await tx.tax.findFirst({
        where: {
          id: data.taxId,
          companyId
        }
      })

      if (!tax) {
        throw new AppError(
          'Vergi oranı bulunamadı',
          400,
          ErrorCodes.PRODUCT_TAX_NOT_FOUND
        )
      }
    }
  }

  private async checkUniqueConstraints(
    tx: Prisma.TransactionClient,
    companyId: string,
    code: string,
    barcode?: string,
    excludeId?: string
  ): Promise<void> {
    // Code uniqueness
    const existingCode = await tx.product.findFirst({
      where: {
        companyId,
        code,
        deletedAt: null,
        ...(excludeId && { id: { not: excludeId } })
      }
    })

    if (existingCode) {
      throw new AppError(
        'Bu ürün kodu zaten kullanılıyor',
        400,
        ErrorCodes.PRODUCT_CODE_EXISTS
      )
    }

    // Barcode uniqueness
    if (barcode) {
      const existingBarcode = await tx.product.findFirst({
        where: {
          companyId,
          barcode,
          deletedAt: null,
          ...(excludeId && { id: { not: excludeId } })
        }
      })

      if (existingBarcode) {
        throw new AppError(
          'Bu barkod zaten kullanılıyor',
          400,
          ErrorCodes.PRODUCT_BARCODE_EXISTS
        )
      }
    }
  }

  /**
   * ✅ Kural 14: Transaction pattern ile update
   */
  async updateProduct(
    companyId: string,
    productId: string,
    data: UpdateProductInput
  ): Promise<ApiResponse<Product>> {
    try {
      logger.info('Updating product', { companyId, productId, data })

      const product = await prisma.$transaction(async (tx) => {
        // 1. Check if product exists
        const existingProduct = await tx.product.findFirst({
          where: {
            id: productId,
            companyId,
            deletedAt: null
          }
        })

        if (!existingProduct) {
          throw new AppError(
            'Ürün bulunamadı',
            404,
            ErrorCodes.PRODUCT_NOT_FOUND
          )
        }

        // 2. Validations
        await this.validateProductData(tx, companyId, data)

        // 3. Check unique constraints (exclude current product)
        if (data.code || data.barcode) {
          await this.checkUniqueConstraints(
            tx,
            companyId,
            data.code || existingProduct.code,
            data.barcode || existingProduct.barcode || undefined,
            productId
          )
        }

        // 4. Update product
        const updatedProduct = await tx.product.update({
          where: { id: productId },
          data: {
            ...data,
            version: { increment: 1 } // Optimistic locking
          },
          include: {
            category: true,
            tax: true,
            variants: {
              where: { deletedAt: null },
              orderBy: { displayOrder: 'asc' }
            }
          }
        })

        logger.info('Product updated successfully', { productId })
        return updatedProduct
      })

      return {
        success: true,
        data: transformProduct(product),
        message: 'Ürün başarıyla güncellendi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to update product', { error, companyId, productId, data })
      throw new AppError(
        'Ürün güncellenemedi',
        500,
        ErrorCodes.PRODUCT_UPDATE_FAILED,
        error
      )
    }
  }

  /**
   * ✅ Soft delete pattern
   */
  async deleteProduct(companyId: string, productId: string): Promise<ApiResponse<null>> {
    try {
      logger.info('Deleting product', { companyId, productId })

      await prisma.$transaction(async (tx) => {
        // 1. Check if product exists
        const product = await tx.product.findFirst({
          where: {
            id: productId,
            companyId,
            deletedAt: null
          }
        })

        if (!product) {
          throw new AppError(
            'Ürün bulunamadı',
            404,
            ErrorCodes.PRODUCT_NOT_FOUND
          )
        }

        // 2. Soft delete product
        await tx.product.update({
          where: { id: productId },
          data: {
            deletedAt: new Date(),
            active: false
          }
        })

        // 3. Soft delete variants
        await tx.productVariant.updateMany({
          where: { productId },
          data: {
            deletedAt: new Date(),
            active: false
          }
        })

        logger.info('Product deleted successfully', { productId })
      })

      return {
        success: true,
        message: 'Ürün başarıyla silindi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to delete product', { error, companyId, productId })
      throw new AppError(
        'Ürün silinemedi',
        500,
        ErrorCodes.PRODUCT_DELETE_FAILED,
        error
      )
    }
  }

  /**
   * ✅ Get categories for dropdown
   */
  async getCategories(companyId: string): Promise<ApiResponse<Category[]>> {
    try {
      const categories = await prisma.category.findMany({
        where: {
          companyId,
          deletedAt: null,
          active: true
        },
        select: {
          id: true,
          name: true,
          color: true,
          icon: true,
          displayOrder: true
        },
        orderBy: { displayOrder: 'asc' }
      })

      return {
        success: true,
        data: categories as Category[]
      }
    } catch (error) {
      logger.error('Failed to fetch categories', { error, companyId })
      throw new AppError(
        'Kategoriler getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }

  /**
   * ✅ Get taxes for dropdown
   */
  async getTaxes(companyId: string): Promise<ApiResponse<Array<{ id: string; name: string; rate: number; type: string }>>> {
    try {
      const taxes = await prisma.tax.findMany({
        where: {
          companyId,
          active: true
        },
        select: {
          id: true,
          name: true,
          rate: true,
          type: true
        },
        orderBy: { name: 'asc' }
      })

      return {
        success: true,
        data: taxes.map(tax => ({
          ...tax,
          rate: Number(tax.rate)
        }))
      }
    } catch (error) {
      logger.error('Failed to fetch taxes', { error, companyId })
      throw new AppError(
        'Vergi oranları getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }
}

// ✅ Kural 8: Export pattern
export const productService = new ProductService()
